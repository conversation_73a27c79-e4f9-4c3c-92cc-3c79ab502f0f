package de.interzero.oneepr.customer.customer_commitment;

import de.interzero.oneepr.admin.fraction_icon.FractionIconRepository;
import de.interzero.oneepr.admin.service_setup.ServiceSetupService;
import de.interzero.oneepr.admin.service_setup.dto.CommitmentPackagingServiceDto;
import de.interzero.oneepr.admin.service_setup.dto.FormattedCommitmentDto;
import de.interzero.oneepr.admin.service_setup.dto.ServiceSetupCommitmentResponseDto;
import de.interzero.oneepr.admin.service_setup.dto.SubmitCommitmentDto;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.Criteria;
import de.interzero.oneepr.common.service.AwsService;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer_commitment.dto.*;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import de.interzero.oneepr.customer.shopping_cart.ShoppingCartRepository;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ResponseStatusException;

import java.time.Duration;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Service class for managing CustomerCommitment entities.
 */
@Service
@RequiredArgsConstructor
public class CustomerCommitmentService {

    private static final Logger logger = LoggerFactory.getLogger(CustomerCommitmentService.class);

    private final CustomerCommitmentRepository customerCommitmentRepository;

    private final CustomerCommitmentMapper customerCommitmentMapper;

    private final ShoppingCartRepository shoppingCartRepository;

    private final ServiceSetupService serviceSetupService;

    private final FractionIconRepository fractionIconRepository;

    private final AwsService awsService;

    /**
     * Creates or updates a customer commitment.
     * <p>
     * This method orchestrates the process of submitting commitment data to an external admin service,
     * then uses the response to create or update a local CustomerCommitment record.
     *
     * @param createDto The DTO containing the initial commitment data.
     * @return The persisted CustomerCommitment entity.
     * @throws ResponseStatusException if input validation fails or the external service call fails.
     * @ts-legacy This method directly translates the logic from the NestJS `create` method,
     * including the initial validation checks, the payload transformation for the external API,
     * the use of a placeholder interface for the HTTP call, and the final upsert logic.
     */
    @Transactional
    public CustomerCommitment create(CreateCustomerCommitmentDto createDto) {

        if (createDto.getYear() == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Year is required");
        }

        if (CollectionUtils.isEmpty(createDto.getCommitmentAnswers())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Commitment answers are invalid");
        }

        if (createDto.getShoppingCartId() != null) {
            shoppingCartRepository.findByIdAndDeletedAtIsNull(createDto.getShoppingCartId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "Shopping cart not found"));
        }
        SubmitCommitmentDto submitCommitmentDto = new SubmitCommitmentDto();
        submitCommitmentDto.setYear(createDto.getYear());
        submitCommitmentDto.setCommitment(createDto.getCommitmentAnswers().stream().map(answer -> {
            SubmitCommitmentDto.CommitmentAnswerDto commitmentAnswerDto = new SubmitCommitmentDto.CommitmentAnswerDto();
            commitmentAnswerDto.setId(answer.getCriteriaId());
            commitmentAnswerDto.setAnswer(answer.getAnswer());
            return commitmentAnswerDto;
        }).toList());

        ServiceSetupCommitmentResponseDto commitmentSubmission = serviceSetupService.submitServiceSetupCommitment(createDto.getCountryCode(),
                                                                                                                  submitCommitmentDto);

        boolean isLicenseRequired = commitmentSubmission.getSetup()
                .getPackagingServices()
                .stream()
                .anyMatch(CommitmentPackagingServiceDto::isObliged);

        CustomerCommitment commitmentToSave = customerCommitmentRepository.findByCustomerEmailAndCountryCodeAndYear(
                createDto.getCustomerEmail(),
                createDto.getCountryCode(),
                Integer.parseInt(commitmentSubmission.getYear())).orElse(new CustomerCommitment());
        putSavedCommitmentsToSubmission(commitmentToSave, commitmentSubmission);

        customerCommitmentMapper.updateFromSubmission(
                commitmentToSave,
                createDto,
                commitmentSubmission,
                isLicenseRequired);

        return customerCommitmentRepository.save(commitmentToSave);
    }

    private static void putSavedCommitmentsToSubmission(CustomerCommitment commitmentToSave,
                                                        ServiceSetupCommitmentResponseDto commitmentSubmission) {
        if (commitmentToSave.getCommitment() == null || commitmentToSave.getCommitment().isEmpty()) {
            return;
        }
        List<FormattedCommitmentDto> formattedCommitmentDtos = commitmentToSave.getCommitment()
                .entrySet()
                .stream()
                .map(c -> {
                    FormattedCommitmentDto formattedCommitmentDto = new FormattedCommitmentDto();
                    Criteria criteria = new Criteria();
                    criteria.setId(Integer.parseInt(c.getKey()));
                    formattedCommitmentDto.setCriteria(criteria);
                    formattedCommitmentDto.setAnswer((String) c.getValue());
                    return formattedCommitmentDto;
                })
                .toList();
        formattedCommitmentDtos.forEach(formattedCommitmentDto -> {
            FormattedCommitmentDto updatedCommitment = commitmentSubmission.getCommitment()
                    .stream()
                    .filter(c -> c.getCriteria().getId().equals(formattedCommitmentDto.getCriteria().getId()))
                    .findFirst()
                    .orElseThrow();
            if (StringUtils.isBlank(updatedCommitment.getAnswer())) {
                updatedCommitment.setAnswer(formattedCommitmentDto.getAnswer());
            }
        });
    }

    /**
     * Updates an existing customer commitment after validating user permissions.
     * This method performs a partial update, only changing the fields provided in the DTO.
     *
     * @param id        The ID of the customer commitment to update.
     * @param updateDto A DTO containing the fields to be updated.
     * @param user      The authenticated user, for permission checking.
     * @return The updated CustomerCommitment entity.
     * @throws ResponseStatusException if the commitment is not found or the user lacks permission.
     */
    @Transactional
    public CustomerCommitment update(Integer id,
                                     UpdateCustomerCommitmentDto updateDto,
                                     AuthenticatedUser user) {
        validatingUserPermissionCustomerCommitment(id, user);

        CustomerCommitment existingCommitment = customerCommitmentRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        "CustomerCommitment not found with ID: " + id));

        customerCommitmentMapper.partialUpdate(existingCommitment, updateDto);

        return customerCommitmentRepository.save(existingCommitment);
    }

    /**
     * Finds all customer commitments based on the provided query filters.
     *
     * @param query DTO containing filter criteria.
     * @return A list of CustomerCommitment entities.
     */
    @Transactional(readOnly = true)
    public List<CustomerCommitment> findAll(FindCustomerCommitmentDto query) {
        if (query.getCustomerEmail() == null || query.getCustomerEmail().isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Customer email is required");
        }
        if (query.getYear() == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Year is required");
        }

        if (query.getCountryCode() != null && !query.getCountryCode().isEmpty()) {
            return customerCommitmentRepository.findAllByCustomerEmailAndYearAndCountryCodeAndDeletedAtIsNull(
                            query.getCustomerEmail(),
                            query.getYear(),
                            query.getCountryCode())
                    .stream()
                    .map(this::setFractionUrl)
                    .toList();
        } else {
            return customerCommitmentRepository.findAllByCustomerEmailAndYearAndDeletedAtIsNull(
                            query.getCustomerEmail(),
                            query.getYear())
                    .stream()
                    .map(this::setFractionUrl)
                    .toList();
        }
    }

    /**
     * Finds a single customer commitment by its ID, after validating user permission.
     *
     * @param id   The ID of the customer commitment.
     * @param user The authenticated user.
     * @return The found CustomerCommitment entity.
     */
    @Transactional(readOnly = true)
    public CustomerCommitment findOne(Integer id,
                                      AuthenticatedUser user) {
        return validatingUserPermissionCustomerCommitment(id, user);
    }


    /**
     * Soft deletes a customer commitment by setting its 'deletedAt' field.
     *
     * @param id   The ID of the customer commitment to remove.
     * @param user The authenticated user.
     * @return The updated (soft-deleted) CustomerCommitment entity.
     */
    @Transactional
    public CustomerCommitment remove(Integer id,
                                     AuthenticatedUser user) {
        CustomerCommitment commitment = validatingUserPermissionCustomerCommitment(id, user);
        commitment.setDeletedAt(LocalDate.now());
        return customerCommitmentRepository.save(commitment);
    }

    /**
     * Determines the commitment status for a list of countries for a specific customer and year.
     *
     * @param customerEmail The customer's email.
     * @param year          The commitment year.
     * @param countryCodes  The list of country codes to check.
     * @return A List of CommitmentStatusDto objects, each representing the status for a country.
     */
    public List<CommitmentStatusDto> getCommitmentStatuses(String customerEmail,
                                                           int year,
                                                           List<String> countryCodes) {
        if (customerEmail == null || countryCodes == null || countryCodes.isEmpty()) {
            return Collections.emptyList();
        }

        // 1. Fetch all existing commitments for the given countries in one DB call.
        List<CustomerCommitment> completedCommitments = customerCommitmentRepository.findByCustomerEmailAndYearAndCountryCodeIn(customerEmail,
                                                                                                                                year,
                                                                                                                                countryCodes);

        // 2. Extract the country codes from the results for quick lookup.
        // Using a Set provides O(1) average time complexity for the 'contains' check.
        Set<String> completedCountryCodes = completedCommitments.stream()
                .map(CustomerCommitment::getCountryCode)
                .collect(Collectors.toSet());

        // 3. Build the result list of DTOs.
        return countryCodes.stream().map(countryCode -> {
            CommitmentStatusDto.Status status = completedCountryCodes.contains(countryCode) ? CommitmentStatusDto.Status.COMPLETED : CommitmentStatusDto.Status.OPEN;
            return new CommitmentStatusDto(countryCode, status);
        }).toList();
    }

    /**
     * Validates if the authenticated user has permission to access/modify the customer commitment.
     *
     * @param id   The ID of the customer commitment.
     * @param user The authenticated user.
     * @return The fetched CustomerCommitment entity if validation passes.
     */
    private CustomerCommitment validatingUserPermissionCustomerCommitment(Integer id,
                                                                          AuthenticatedUser user) {
        if (id == null || id <= 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid CustomerCommitment ID");
        }
        CustomerCommitment commitment = customerCommitmentRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer commitment not found"));
        setFractionUrl(commitment);
        if (user.getRole() == Role.CUSTOMER) {
            Customer customer = commitment.getCustomer();
            if (customer == null || customer.getUserId() == null) {
                logger.warn(
                        "Attempt by CUSTOMER role user ({}) to access commitment ({}) with no associated customer.",
                        user.getId(),
                        id);
                throw new ResponseStatusException(
                        HttpStatus.FORBIDDEN,
                                                  "Cannot verify permission for this customer commitment.");
            }
            int userIdFromAuth;
            try {
                userIdFromAuth = Integer.parseInt(user.getId());
            } catch (NumberFormatException e) {
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "User ID format is invalid.", e);
            }

            if (!Objects.equals(customer.getUserId(), userIdFromAuth)) {
                throw new ResponseStatusException(
                        HttpStatus.FORBIDDEN,
                                                  "You do not have permission to access this customer commitment");
            }
        }
        return commitment;
    }

    /**
     * Enriches a customer commitment by setting presigned URLs for all fraction icons.
     * <p>
     * This method traverses the hierarchical structure of packaging services and their
     * associated report sets to populate fraction icon URLs with AWS presigned URLs.
     * The traversal handles nested fractions recursively to ensure all levels are processed.
     *
     * @param customerCommitment The customer commitment to enrich with fraction icon URLs.
     *                           Must not be null and must have a valid service setup.
     * @return The same customer commitment instance with populated fraction icon URLs.
     * @throws NumberFormatException if any fraction icon ID cannot be parsed as an integer.
     * @see #enrichFractionWithIconUrl(ReportSet.Fraction)
     * @see #generatePresignedUrl(String)
     */
    private CustomerCommitment setFractionUrl(CustomerCommitment customerCommitment) {
        List<CustomerServiceSetup.PackagingServiceExtended> packagingServices = customerCommitment.getServiceSetup()
                .getPackagingServices();
        if (packagingServices == null) {
            logger.info("No packaging services found for customer commitment ID: {}", customerCommitment.getId());
            return customerCommitment;
        }

        packagingServices.forEach(this::enrichPackagingServiceWithFractionUrls);
        return customerCommitment;
    }

    /**
     * Enriches a single packaging service by setting presigned URLs for all its fraction icons.
     * <p>
     * This method processes the report set associated with the packaging service and
     * recursively enriches all fractions and their children with icon URLs.
     *
     * @param packagingService The packaging service to process. Must not be null.
     */
    private void enrichPackagingServiceWithFractionUrls(CustomerServiceSetup.PackagingServiceExtended packagingService) {
        if (packagingService == null || packagingService.getReportSet() == null) {
            logger.info("Skipping packaging service: service or report set is null");
            return;
        }

        ReportSet reportSet = packagingService.getReportSet();
        List<ReportSet.Fraction> topLevelFractions = reportSet.getFractions();

        if (topLevelFractions != null) {
            topLevelFractions.forEach(this::enrichFractionWithIconUrl);
        }
    }

    /**
     * Recursively enriches a fraction and all its children with presigned icon URLs.
     * <p>
     * This method performs a depth-first traversal of the fraction hierarchy,
     * setting the fraction icon URL for each fraction by generating a presigned
     * AWS URL for the associated icon image.
     *
     * @param fraction The fraction to enrich. Must not be null.
     * @throws NumberFormatException if the fraction icon ID cannot be parsed as an integer.
     * @see #generatePresignedUrl(String)
     */
    private void enrichFractionWithIconUrl(ReportSet.Fraction fraction) {
        if (fraction == null) {
            logger.info("Skipping null fraction");
            return;
        }

        // Set the icon URL for the current fraction
        setIconUrlForFraction(fraction);

        // Recursively process all child fractions
        List<ReportSet.Fraction> childFractions = fraction.getChildren();
        if (childFractions != null && !childFractions.isEmpty()) {
            childFractions.forEach(this::enrichFractionWithIconUrl);
        }
    }

    /**
     * Sets the presigned icon URL for a single fraction.
     * <p>
     * This method retrieves the fraction icon from the database using the fraction's
     * icon ID and generates a presigned URL for downloading the icon image.
     *
     * @param fraction The fraction for which to set the icon URL. Must not be null.
     * @throws NumberFormatException if the fraction icon ID cannot be parsed as an integer.
     */
    private void setIconUrlForFraction(ReportSet.Fraction fraction) {
        Integer fractionIconId = Integer.parseInt(fraction.getFractionIconId());

        fractionIconRepository.findByIdAndDeletedAtIsNull(fractionIconId).ifPresentOrElse(
                fractionIcon -> {
                    String presignedUrl = generatePresignedUrl(fractionIcon.getImageUrl());
                    fraction.setFractionIconUrl(presignedUrl);
                },
                () -> logger.warn(
                        "Fraction icon not found for ID: {} (fraction: {})",
                        fractionIconId,
                        fraction.getId()));
    }

    private String generatePresignedUrl(String imageUrl) {
        return awsService.generatePresignedDownloadUrl(imageUrl, Duration.ofMinutes(15)).getPreSignedUrl();
    }


}
