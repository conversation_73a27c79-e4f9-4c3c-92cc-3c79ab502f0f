package de.interzero.oneepr.customer.customer_commitment;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer_commitment.dto.CustomerServiceSetup;
import de.interzero.oneepr.customer.shopping_cart.ShoppingCart;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(
        name = "customer_commitment",
        uniqueConstraints = {@UniqueConstraint(columnNames = {"customer_email", "country_code", "year"})}
)
public class CustomerCommitment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @Column(name = "customer_email")
    @JsonProperty("customer_email")
    private String customerEmail;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "customer_email",
            referencedColumnName = "email",
            insertable = false,
            updatable = false
    )
    @JsonIgnore
    @JsonProperty("customer")
    private Customer customer;

    @NotNull
    @Column(
            name = "country_code",
            nullable = false
    )
    @JsonProperty("country_code")
    private String countryCode;

    @NotNull
    @Column(
            name = "year",
            nullable = false
    )
    @JsonProperty("year")
    private Integer year;

    @NotNull
    @Column(
            name = "commitment",
            nullable = false,
            columnDefinition = "jsonb"
    )
    @JdbcTypeCode(SqlTypes.JSON)
    @JsonProperty("commitment")
    private Map<String, Object> commitment;

    @Column(
            name = "service_setup",
            columnDefinition = "jsonb"
    )
    @JdbcTypeCode(SqlTypes.JSON)
    @JsonProperty("service_setup")
    private CustomerServiceSetup serviceSetup;

    @NotNull
    @Column(
            name = "is_license_required",
            nullable = false
    )
    @JsonProperty("is_license_required")
    private Boolean isLicenseRequired = false;

    @NotNull
    @Column(
            name = "blame",
            nullable = false,
            columnDefinition = "jsonb"
    )
    @JdbcTypeCode(SqlTypes.JSON)
    @JsonProperty("blame")
    private Map<String, Object> blame;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false,
            updatable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;


    @Column(name = "shopping_cart_id")
    private String shoppingCartId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "shopping_cart_id",
            referencedColumnName = "id",
            insertable = false,
            updatable = false
    )
    @JsonProperty("shopping_cart")
    private ShoppingCart shoppingCart;

    @Transient
    @JsonProperty("shopping_cart_id")
    public String getShoppingCartIdTransient() {
        return this.shoppingCartId;
    }

    @PrePersist
    protected void onCreate() {
        this.createdAt = Instant.now();
        this.updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Instant.now();
    }
}