import { Body, Controller, Get, Param, Post, Query } from "@nestjs/common";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import { ServiceSetupService } from "./service-setup.service";
import { CriteriaType } from "@prisma/client";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";
import { PublicRoute } from "@/shared/auth/public-route.decorator";
import { SubmitCommitmentDto } from "./dto/submit-commitment.dto";
import { CalculateLicenseCostsDto } from "./dto/calculate-license-costs.dto";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK)
@ApiTags("ServiceSetup")
@Controller("service-setups")
export class ServiceSetupController {
  constructor(private readonly serviceSetupService: ServiceSetupService) {}

  @Get(":countryCode")
  @ApiOperation({ summary: "Get service setup by country code" })
  findServiceSetup(@Param("countryCode") countryCode: string) {
    return this.serviceSetupService.findServiceSetup(countryCode);
  }

  @Get(":countryCode/status")
  @ApiOperation({ summary: "Get service setup status by country code" })
  getServiceSetupStatus(@Param("countryCode") countryCode: string) {
    return this.serviceSetupService.getServiceSetupStatus(countryCode);
  }

  @Get(":countryCode/packaging-services")
  @ApiOperation({ summary: "Get service setup packaging services" })
  findServiceSetupPackagingServices(@Param("countryCode") countryCode: string) {
    return this.serviceSetupService.findServiceSetupPackagingServices(countryCode);
  }

  @Get(":countryCode/report-sets")
  @ApiOperation({ summary: "Get service setup report sets" })
  findServiceSetupReportSets(@Param("countryCode") countryCode: string) {
    return this.serviceSetupService.findServiceSetupReportSets(countryCode);
  }

  @Get(":countryCode/report-sets/:reportSetId")
  @ApiOperation({ summary: "Get service setup report set" })
  findServiceSetupReportSet(@Param("countryCode") countryCode: string, @Param("reportSetId") reportSetId: number) {
    return this.serviceSetupService.findServiceSetupReportSet(countryCode, Number(reportSetId));
  }

  @Get(":countryCode/report-frequencies")
  @ApiOperation({ summary: "Get service setup report frequencies" })
  findServiceSetupReportFrequencies(@Param("countryCode") countryCode: string) {
    return this.serviceSetupService.findServiceSetupReportFrequencies(countryCode);
  }

  @Get(":countryCode/representative-tiers")
  @ApiOperation({ summary: "Get service setup representatives and other costs" })
  findServiceSetupRepresentativeTiers(@Param("countryCode") countryCode: string) {
    return this.serviceSetupService.findServiceSetupRepresentativeTiers(countryCode);
  }

  @Get(":countryCode/other-costs")
  @ApiOperation({ summary: "Get service setup representatives and other costs" })
  findServiceSetupOtherCosts(@Param("countryCode") countryCode: string) {
    return this.serviceSetupService.findServiceSetupOtherCosts(countryCode);
  }

  @PublicRoute()
  @Get(":countryCode/price-lists")
  @ApiOperation({ summary: "Get service setup price lists" })
  findServiceSetupPriceLists(@Param("countryCode") countryCode: string) {
    return this.serviceSetupService.findServiceSetupPriceLists(countryCode);
  }

  @Get(":countryCode/required-informations")
  @ApiOperation({ summary: "Get service setup required informations" })
  findServiceSetupRequiredInformations(@Param("countryCode") countryCode: string) {
    return this.serviceSetupService.findServiceSetupRequiredInformations(countryCode);
  }

  @Get(":countryCode/criterias")
  @ApiOperation({ summary: "Get service setup criterias" })
  findServiceSetupCriterias(
    @Param("countryCode") countryCode: string,
    @Query("type") type: CriteriaType,
    @Query("required_information_id") requiredInformationId: number,
    @Query("packaging_service_id") packagingServiceId: number
  ) {
    return this.serviceSetupService.findServiceSetupCriterias({
      countryCode,
      type,
      requiredInformationId,
      packagingServiceId,
    });
  }

  @PublicRoute()
  @Get(":countryCode/commitment")
  @ApiOperation({ summary: "Get service setup commitment" })
  findServiceSetupCommitment(@Param("countryCode") countryCode: string) {
    console.info("GET COMMITMENT FOR: ", countryCode);
    return this.serviceSetupService.findServiceSetupCommitment(countryCode);
  }

  @PublicRoute()
  @Post(":countryCode/commitment")
  @ApiOperation({ summary: "Submit service setup commitment" })
  submitServiceSetupCommitment(@Param("countryCode") countryCode: string, @Body() data: SubmitCommitmentDto) {
    console.info("SUBMIT COMMITMENT FOR: ", countryCode);
    return this.serviceSetupService.submitServiceSetupCommitment(countryCode, data);
  }

  @PublicRoute()
  @Post(":countryCode/calculator")
  @ApiOperation({ summary: "Calculate license costs" })
  calculateLicenseCosts(@Param("countryCode") countryCode: string, @Body() data: CalculateLicenseCostsDto) {
    console.info("CALCULATE LICENSE COSTS FOR: ", countryCode);
    return this.serviceSetupService.calculateLicenseCosts(countryCode, data);
  }
}
